{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@tabler/icons-react": "^3.33.0", "@tailwindcss/vite": "^4.1.7", "clsx": "^2.1.1", "framer-motion": "^12.12.2", "motion": "^12.12.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "3.5.3", "vite": "^6.3.5"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}