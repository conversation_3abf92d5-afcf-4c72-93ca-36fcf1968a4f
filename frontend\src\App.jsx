import { Outlet } from "react-router-dom";
import "./App.css";
import TopHeaderNotification from "./Components/Notifications/TopHeaderNotification";
import PhoneNavBar from "./Components/Header/PhoneNavBar";
import DesktopNavBar from "./Components/Header/DesktopNavBar";

function App() {
  return (
    <div className="flex flex-col min-h-screen">
      <TopHeaderNotification />

      {/* Desktop Navigation */}
      <div className="hidden lg:block">
        <DesktopNavBar />
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <PhoneNavBar />
      </div>

      <main className="flex-grow lg:pt-32">
        <Outlet />
      </main>
    </div>
  );
}

export default App;
