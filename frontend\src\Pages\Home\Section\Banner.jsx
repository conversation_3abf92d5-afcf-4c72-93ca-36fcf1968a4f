import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronLeft, FaChevronRight, FaPlay } from "react-icons/fa";

const Banner = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  const slides = [
    {
      id: 1,
      image:
        "https://apis.lalpathlabsnp.com/api/v1/uploads/s3/download-image?imageKey=apis/lpl-nepal/791ef493-74bb-49d6-9f9c-0a0a761db84c.webp",
    },
    {
      id: 2,
      image:
        "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    },
    {
      id: 3,
      image:
        "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    },
  ];

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlay) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slides.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlay, slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  return (
    <div className="relative w-full h-[85vh] overflow-hidden">
      {/* Image Slider */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0, scale: 1.02 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.98 }}
          transition={{ duration: 1.2, ease: "easeInOut" }}
          className="absolute inset-0"
        >
          <img
            src={slides[currentSlide].image}
            alt={`Medical facility ${currentSlide + 1}`}
            className="w-full h-full object-cover"
          />
        </motion.div>
      </AnimatePresence>

      {/* Navigation Arrows - Minimal Design */}
      <motion.button
        whileHover={{
          scale: 1.1,
          backgroundColor: "rgba(255, 255, 255, 0.15)",
        }}
        whileTap={{ scale: 0.95 }}
        onClick={prevSlide}
        className="absolute left-8 top-1/2 transform -translate-y-1/2 z-30 bg-white/10 backdrop-blur-md rounded-full p-3 text-white/80 hover:text-white transition-all duration-300 border border-white/20"
      >
        <FaChevronLeft className="text-base" />
      </motion.button>

      <motion.button
        whileHover={{
          scale: 1.1,
          backgroundColor: "rgba(255, 255, 255, 0.15)",
        }}
        whileTap={{ scale: 0.95 }}
        onClick={nextSlide}
        className="absolute right-8 top-1/2 transform -translate-y-1/2 z-30 bg-white/10 backdrop-blur-md rounded-full p-3 text-white/80 hover:text-white transition-all duration-300 border border-white/20"
      >
        <FaChevronRight className="text-base" />
      </motion.button>

      {/* Slide Indicators - Elegant Design */}
      <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 z-30 flex space-x-4">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            whileHover={{ scale: 1.3 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => goToSlide(index)}
            className={`transition-all duration-500 ${
              index === currentSlide
                ? "w-8 h-2 bg-white rounded-full"
                : "w-2 h-2 bg-white/40 hover:bg-white/70 rounded-full"
            }`}
          />
        ))}
      </div>

      {/* Auto-play Control - Minimal */}
      <motion.button
        whileHover={{
          scale: 1.05,
          backgroundColor: "rgba(255, 255, 255, 0.15)",
        }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsAutoPlay(!isAutoPlay)}
        className="absolute top-8 right-8 z-30 bg-white/10 backdrop-blur-md rounded-full p-2.5 text-white/70 hover:text-white transition-all duration-300 border border-white/20"
      >
        <span className="text-sm">{isAutoPlay ? "⏸" : "▶"}</span>
      </motion.button>
    </div>
  );
};

export default Banner;
