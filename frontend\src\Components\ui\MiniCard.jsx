import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaFileDownload,
  FaMapMarkerAlt,
  FaFlask,
  FaStethoscope,
  FaHeartbeat,
  FaUserMd,
} from "react-icons/fa";

const MiniCard = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const serviceCards = [
    {
      id: 1,
      icon: FaFileDownload,
      title: "Download Report",
    },
    {
      id: 2,
      icon: FaMapMarkerAlt,
      title: "Nearest Center",
    },
    {
      id: 3,
      icon: FaFlask,
      title: "Book A Test",
    },
    {
      id: 4,
      icon: FaStethoscope,
      title: "Health Checkup",
    },
    {
      id: 5,
      icon: FaHeartbeat,
      title: "Emergency Tests",
    },
    {
      id: 6,
      icon: FaUserMd,
      title: "Consult Doctor",
    },
  ];

  const totalSlides = Math.ceil(serviceCards.length / 3);

  const getCurrentCards = () => {
    const startIndex = currentSlide * 3;
    return serviceCards.slice(startIndex, startIndex + 3);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  return (
    <div
      className="py-16 px-6 bg-gray-50"
      style={{ fontFamily: "Inter, system-ui, -apple-system, sans-serif" }}
    >
      <div className="max-w-5xl mx-auto">
        {/* Cards Container */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
          {getCurrentCards().map((card) => {
            const IconComponent = card.icon;

            return (
              <div
                key={card.id}
                className="relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer group overflow-hidden border border-gray-100 hover:border-gray-200 hover:-translate-y-2"
              >
                {/* Minimalist Top Accent */}
                <div className="absolute top-0 left-0 right-0 h-1 bg-black"></div>

                {/* Card Content */}
                <div className="p-8 text-center relative z-10">
                  {/* Icon Container */}
                  <div className="mb-6 flex justify-center">
                    <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:shadow-xl transition-all duration-300">
                      <IconComponent className="text-2xl text-white" />
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-medium text-gray-900 group-hover:text-black transition-colors duration-300 tracking-wide leading-relaxed">
                    {card.title}
                  </h3>
                </div>

                {/* Subtle Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-gray-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
              </div>
            );
          })}
        </div>

        {/* Modern Dots Indicator */}
        {totalSlides > 1 && (
          <div className="flex justify-center space-x-3">
            {Array.from({ length: totalSlides }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`transition-all duration-300 ${
                  index === currentSlide
                    ? "w-8 h-2 bg-black rounded-full"
                    : "w-2 h-2 bg-gray-400 rounded-full hover:bg-gray-600"
                }`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MiniCard;
