import React from "react";
import { 
  FaFileDownload, 
  FaMapMarkerAlt, 
  FaFlask,
  FaStethoscope,
  FaHeartbeat,
  FaUserMd 
} from "react-icons/fa";

const ServiceCard = ({ icon, title, description, onClick, className = "" }) => {
  const IconComponent = icon;

  return (
    <div 
      className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group overflow-hidden ${className}`}
      onClick={onClick}
    >
      {/* Card Content */}
      <div className="p-6 text-center">
        {/* Icon Container */}
        <div className="mb-4 flex justify-center">
          <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <IconComponent className="text-2xl text-white" />
          </div>
        </div>
        
        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-300">
          {title}
        </h3>
        
        {/* Description */}
        {description && (
          <p className="text-sm text-gray-600 leading-relaxed">
            {description}
          </p>
        )}
      </div>

      {/* Blue Border Effect */}
      <div className="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-blue-500 to-blue-700 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
      
      {/* Corner Decorations */}
      <div className="absolute top-0 left-0 w-8 h-8 border-l-4 border-t-4 border-blue-500 rounded-tl-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div className="absolute bottom-0 right-0 w-8 h-8 border-r-4 border-b-4 border-blue-500 rounded-br-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

// Pre-defined service cards data
export const serviceCardsData = [
  {
    id: 1,
    icon: FaFileDownload,
    title: "Download Report",
    description: "Access your test results instantly",
    link: "/download-report"
  },
  {
    id: 2,
    icon: FaMapMarkerAlt,
    title: "Nearest Center",
    description: "Find the closest PathLab location",
    link: "/locations"
  },
  {
    id: 3,
    icon: FaFlask,
    title: "Book A Test",
    description: "Schedule your lab tests online",
    link: "/book-test"
  },
  {
    id: 4,
    icon: FaStethoscope,
    title: "Health Checkup",
    description: "Comprehensive health packages",
    link: "/health-checkup"
  },
  {
    id: 5,
    icon: FaHeartbeat,
    title: "Emergency Tests",
    description: "Urgent diagnostic services",
    link: "/emergency"
  },
  {
    id: 6,
    icon: FaUserMd,
    title: "Consult Doctor",
    description: "Expert medical consultation",
    link: "/consultation"
  }
];

// Service Cards Container Component
export const ServiceCardsContainer = ({ cards = serviceCardsData.slice(0, 3), className = "" }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
      {cards.map((card) => (
        <ServiceCard
          key={card.id}
          icon={card.icon}
          title={card.title}
          description={card.description}
          onClick={() => {
            if (card.link) {
              window.location.href = card.link;
            }
          }}
        />
      ))}
    </div>
  );
};

export default ServiceCard;
