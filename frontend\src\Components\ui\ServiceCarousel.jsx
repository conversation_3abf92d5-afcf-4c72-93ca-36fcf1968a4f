import React, { useState, useEffect } from "react";
import { 
  FaFileDownload, 
  FaMapMarkerAlt, 
  FaFlask,
  FaStethoscope,
  FaHeartbeat,
  FaUserMd,
  FaChevronLeft,
  FaChevronRight
} from "react-icons/fa";

const ServiceCarousel = ({ autoPlay = true, autoPlayInterval = 4000 }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const serviceCards = [
    {
      id: 1,
      icon: FaFileDownload,
      title: "Download Report",
      description: "Access your test results instantly",
      link: "/download-report",
      iconBg: "from-blue-400 to-blue-600"
    },
    {
      id: 2,
      icon: FaMapMarkerAlt,
      title: "Nearest Center",
      description: "Find the closest PathLab location",
      link: "/locations",
      iconBg: "from-yellow-400 to-orange-500"
    },
    {
      id: 3,
      icon: FaFlask,
      title: "Book A Test",
      description: "Schedule your lab tests online",
      link: "/book-test",
      iconBg: "from-green-400 to-teal-500"
    },
    {
      id: 4,
      icon: FaStethoscope,
      title: "Health Checkup",
      description: "Comprehensive health packages",
      link: "/health-checkup",
      iconBg: "from-purple-400 to-purple-600"
    },
    {
      id: 5,
      icon: FaHeartbeat,
      title: "Emergency Tests",
      description: "Urgent diagnostic services",
      link: "/emergency",
      iconBg: "from-red-400 to-red-600"
    },
    {
      id: 6,
      icon: FaUserMd,
      title: "Consult Doctor",
      description: "Expert medical consultation",
      link: "/consultation",
      iconBg: "from-indigo-400 to-indigo-600"
    }
  ];

  const totalSlides = Math.ceil(serviceCards.length / 3);

  // Auto-play functionality
  useEffect(() => {
    if (autoPlay) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % totalSlides);
      }, autoPlayInterval);

      return () => clearInterval(interval);
    }
  }, [autoPlay, autoPlayInterval, totalSlides]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const getCurrentCards = () => {
    const startIndex = currentSlide * 3;
    return serviceCards.slice(startIndex, startIndex + 3);
  };

  return (
    <div className="relative w-full max-w-6xl mx-auto px-4 py-8">
      {/* Cards Container */}
      <div className="relative overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {getCurrentCards().map((card) => {
            const IconComponent = card.icon;
            
            return (
              <div
                key={card.id}
                className="relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group overflow-hidden"
                onClick={() => {
                  if (card.link) {
                    window.location.href = card.link;
                  }
                }}
              >
                {/* Card Content */}
                <div className="p-6 text-center">
                  {/* Icon Container */}
                  <div className="mb-4 flex justify-center">
                    <div className={`w-16 h-16 bg-gradient-to-br ${card.iconBg} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className="text-2xl text-white" />
                    </div>
                  </div>
                  
                  {/* Title */}
                  <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                    {card.title}
                  </h3>
                  
                  {/* Description */}
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {card.description}
                  </p>
                </div>

                {/* Blue Border Effect */}
                <div className="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-blue-500 to-blue-700 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                
                {/* Corner Decorations */}
                <div className="absolute top-0 left-0 w-8 h-8 border-l-4 border-t-4 border-blue-500 rounded-tl-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-0 right-0 w-8 h-8 border-r-4 border-b-4 border-blue-500 rounded-br-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-blue-50 group"
        disabled={totalSlides <= 1}
      >
        <FaChevronLeft className="text-blue-600 group-hover:text-blue-700" />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-blue-50 group"
        disabled={totalSlides <= 1}
      >
        <FaChevronRight className="text-blue-600 group-hover:text-blue-700" />
      </button>

      {/* Dots Indicator */}
      {totalSlides > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? "bg-blue-600 scale-125"
                  : "bg-gray-300 hover:bg-gray-400"
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ServiceCarousel;
