import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { FaFlask, FaUserMd, FaHeartbeat, FaShieldAlt } from "react-icons/fa";

const Hero = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 200);
    return () => clearTimeout(timer);
  }, []);

  const features = [
    {
      icon: FaFlask,
      title: "Advanced Testing",
      description: "State-of-the-art laboratory equipment",
    },
    {
      icon: FaUserMd,
      title: "Expert Team",
      description: "Qualified medical professionals",
    },
    {
      icon: FaHeartbeat,
      title: "Quick Results",
      description: "Fast and accurate diagnostics",
    },
    {
      icon: FaShieldAlt,
      title: "Trusted Care",
      description: "Your health is our priority",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 30 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      y: -10,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  };

  return (
    <div className="relative bg-white py-20 px-6 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-gray-900 to-black"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Main Hero Content */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${isVisible ? "animate-fadeInUp" : "opacity-0"}`}
        >
          <h1 className="fredoka-font text-5xl md:text-7xl font-bold mb-6 text-gray-900 leading-tight">
            Welcome to{" "}
            <span className="text-gradient bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              PathLab
            </span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Your trusted partner for accurate medical testing with cutting-edge
            technology and expert care
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="btn-primary px-8 py-4 text-lg font-medium">
              Book a Test Now
            </button>
            <button className="btn-secondary px-8 py-4 text-lg font-medium">
              View Services
            </button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className={`card-modern p-6 text-center transition-all duration-700 delay-${index * 100} ${
                  isVisible ? "animate-fadeInScale" : "opacity-0"
                }`}
              >
                <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Icon className="text-2xl text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Stats Section */}
        <div
          className={`mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 transition-all duration-1000 delay-500 ${isVisible ? "animate-fadeInUp" : "opacity-0"}`}
        >
          {[
            { number: "50K+", label: "Tests Completed" },
            { number: "99.9%", label: "Accuracy Rate" },
            { number: "24/7", label: "Support Available" },
            { number: "15+", label: "Years Experience" },
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 fredoka-font">
                {stat.number}
              </div>
              <div className="text-gray-600 text-sm font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-gray-100 rounded-full opacity-50 animate-float"></div>
      <div className="absolute bottom-20 left-20 w-24 h-24 bg-gray-200 rounded-full opacity-30 animate-pulse"></div>
    </div>
  );
};

export default Hero;
