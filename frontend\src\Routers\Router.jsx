import { createBrowserRouter } from "react-router-dom";
import App from "../App";
import HomePage from "../Pages/Home/HomePage";
import DownloadReport from "../Pages/Report/DownloadReport";
import ServicesPage from "../Pages/Services/ServicesPage";

const router = createBrowserRouter([
  {
    path: "/",
    Component: App,
    children: [
      { path: "/", element: <HomePage /> },
      { path: "/service", element: <ServicesPage /> },
      { path: "/download-report", element: <DownloadReport /> },
      //   { path: "/login", element: <Login /> },
      //   { path: "/register", element: <Register /> },
    ],
  },
]);

export default router;
