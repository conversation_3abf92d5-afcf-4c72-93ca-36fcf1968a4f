import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  FaFlask, 
  FaHeartbeat, 
  FaBrain, 
  FaEye, 
  FaBone,
  FaLungs,
  FaSearch,
  FaArrowRight
} from "react-icons/fa";

const ServicesPage = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  const categories = [
    { id: "all", name: "All Services", icon: FaFlask },
    { id: "blood", name: "Blood Tests", icon: FaHeartbeat },
    { id: "imaging", name: "Imaging", icon: FaEye },
    { id: "cardiology", name: "Cardiology", icon: FaHeartbeat },
    { id: "neurology", name: "Neurology", icon: FaBrain }
  ];

  const services = [
    {
      id: 1,
      name: "Complete Blood Count (CBC)",
      category: "blood",
      price: "₹500",
      duration: "2-4 hours",
      description: "Comprehensive blood analysis including RBC, WBC, platelets, and hemoglobin levels.",
      icon: FaHeartbeat
    },
    {
      id: 2,
      name: "Lipid Profile",
      category: "blood",
      price: "₹800",
      duration: "4-6 hours",
      description: "Cholesterol and triglyceride levels assessment for cardiovascular health.",
      icon: FaHeartbeat
    },
    {
      id: 3,
      name: "X-Ray Chest",
      category: "imaging",
      price: "₹600",
      duration: "30 minutes",
      description: "Digital chest X-ray for lung and heart examination.",
      icon: FaLungs
    },
    {
      id: 4,
      name: "ECG",
      category: "cardiology",
      price: "₹400",
      duration: "15 minutes",
      description: "Electrocardiogram to assess heart rhythm and electrical activity.",
      icon: FaHeartbeat
    },
    {
      id: 5,
      name: "MRI Brain",
      category: "neurology",
      price: "₹8000",
      duration: "45 minutes",
      description: "Detailed brain imaging using magnetic resonance technology.",
      icon: FaBrain
    },
    {
      id: 6,
      name: "Bone Density Scan",
      category: "imaging",
      price: "₹2500",
      duration: "30 minutes",
      description: "DEXA scan to measure bone mineral density and assess osteoporosis risk.",
      icon: FaBone
    }
  ];

  const filteredServices = services.filter(service => {
    const matchesCategory = selectedCategory === "all" || service.category === selectedCategory;
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-black text-white py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="fredoka-font text-5xl md:text-6xl font-bold mb-6"
          >
            Our Services
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-gray-300 max-w-2xl mx-auto"
          >
            Comprehensive medical testing services with state-of-the-art technology
          </motion.p>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="py-12 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="relative max-w-md mx-auto mb-8"
          >
            <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-2xl focus:border-black focus:outline-none transition-colors duration-300"
            />
          </motion.div>

          {/* Category Filter */}
          <motion.div
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <motion.button
                  key={category.id}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center gap-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
                    selectedCategory === category.id
                      ? "bg-black text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  <Icon className="text-lg" />
                  {category.name}
                </motion.button>
              );
            })}
          </motion.div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedCategory + searchTerm}
              initial="hidden"
              animate="visible"
              exit="hidden"
              variants={containerVariants}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredServices.map((service) => {
                const Icon = service.icon;
                return (
                  <motion.div
                    key={service.id}
                    variants={itemVariants}
                    whileHover={{ y: -10, scale: 1.02 }}
                    className="card-modern p-6 cursor-pointer group"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="w-12 h-12 bg-black rounded-xl flex items-center justify-center">
                        <Icon className="text-xl text-white" />
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-gray-900">{service.price}</div>
                        <div className="text-sm text-gray-500">{service.duration}</div>
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-black transition-colors">
                      {service.name}
                    </h3>
                    
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {service.description}
                    </p>
                    
                    <motion.button
                      whileHover={{ x: 5 }}
                      className="flex items-center gap-2 text-black font-medium group-hover:gap-3 transition-all duration-300"
                    >
                      Book Now
                      <FaArrowRight className="text-sm" />
                    </motion.button>
                  </motion.div>
                );
              })}
            </motion.div>
          </AnimatePresence>

          {filteredServices.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">No services found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria</p>
            </motion.div>
          )}
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto text-center px-6">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="fredoka-font text-4xl md:text-5xl font-bold mb-6"
          >
            Need Help Choosing?
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-gray-300 mb-8"
          >
            Our medical experts are here to guide you to the right tests
          </motion.p>
          
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="btn-primary bg-white text-black hover:bg-gray-100 px-8 py-4 text-lg"
          >
            Consult Our Experts
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default ServicesPage;
