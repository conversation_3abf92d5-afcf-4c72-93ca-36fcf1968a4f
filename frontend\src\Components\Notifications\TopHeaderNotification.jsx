import React from "react";
import { FaMapMarkerAlt } from "react-icons/fa";

const TopHeaderNotification = () => {
  return (
    <div className="bg-black text-white notification-bar">
      <div className="flex items-center justify-between px-6 py-3">
        <div className="flex items-center space-x-2">
          <h1 className="fredoka-font text-xl font-bold text-gradient bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            PathLab
          </h1>
          <span className="text-gray-400 text-sm">Medical Diagnostics</span>
        </div>

        <div className="flex items-center space-x-2 bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 cursor-pointer">
          <FaMapMarkerAlt className="text-white text-sm" />
          <span className="text-white text-sm font-medium">
            Kathmandu, Nepal
          </span>
        </div>
      </div>
    </div>
  );
};

export default TopHeaderNotification;
