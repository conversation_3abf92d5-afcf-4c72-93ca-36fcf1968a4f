import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaBarsStaggered,
  FaCartShopping,
  FaRightFromBracket,
  FaUserTie,
  FaXmark,
  FaPhone,
  FaHouse,
  FaFilePrescription,
  FaHeadset,
  FaDownload,
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaLinkedinIn,
  FaEnvelope,
  FaMapLocationDot,
  FaSearchengin,
} from "react-icons/fa6";
import { Link, useLocation } from "react-router-dom";

const PhoneNavBar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeNavItem, setActiveNavItem] = useState(1);
  const location = useLocation();

  // Handle scroll event to make navbar sticky
  useEffect(() => {
    const handleScroll = () => {
      // Get the height of the notification bar to determine when to make navbar sticky
      const notificationHeight =
        document.querySelector(".notification-bar")?.offsetHeight || 0;

      if (window.scrollY > notificationHeight) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Clean up event listener
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const NavItems = [
    { id: 1, icon: FaHouse, name: "Home", link: "/" },
    {
      id: 2,
      icon: FaFilePrescription,
      name: "Book A Service",
      link: "/service",
    },
    {
      id: 3,
      icon: FaDownload,
      name: "Download Report",
      link: "/download-report",
    },

    { id: 4, icon: FaHeadset, name: "Request a Call", link: "/request-call" },
  ];

  const SideNavItems = [
    { id: 1, icon: FaUserTie, name: "Account", link: "/account" },
    { id: 2, icon: FaCartShopping, name: "Orders", link: "/orders" },
    { id: 3, icon: FaPhone, name: "Contact", link: "/contact" },
    { id: 4, icon: FaRightFromBracket, name: "Logout", link: "/logout" },
  ];

  return (
    <>
      {/* Main Navigation Bar */}
      <div
        className={`${
          isSticky ? "fixed top-0 left-0 right-0 animate-slideDown" : "relative"
        } bg-white shadow-lg border-b border-gray-200 z-40 transition-all duration-300`}
      >
        <div className="flex items-center justify-between px-4 py-3 gap-3">
          {/* Professional Search Box replacing logo */}
          <div className="flex items-center gap-4 flex-1 max-w-sm">
            <div className="relative flex-1">
              <FaSearchengin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm z-10 transition-colors duration-300" />
              <input
                type="text"
                placeholder="Search tests, packages, health checkups..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="text-black font-semibold w-[90%] pl-8 pr-4 py-3 bg-gray-50 border-2 border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-0 focus:border-blue-500 focus:bg-white transition-all duration-300 shadow-sm hover:shadow-md"
              />
            </div>
          </div>

          <div className="flex items-center gap-4">
            <Link
              to="/cart"
              className="relative hover:text-blue-600 transition-colors duration-300"
            >
              <FaCartShopping className="text-xl" />
              <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </Link>

            <button
              onClick={() => setIsOpen(!isOpen)}
              className="hover:text-blue-600 transition-colors duration-300 relative z-50 p-2 hover:bg-gray-100 rounded-lg"
            >
              {isOpen ? (
                <FaXmark className="text-xl" />
              ) : (
                <FaBarsStaggered className="text-xl" />
              )}
            </button>
          </div>
        </div>

        {/* Desktop/Tablet Navigation Items */}
        <div className="hidden md:block border-t border-gray-100">
          <ul className="flex items-center justify-center py-2 px-4 gap-8">
            {NavItems.map((item) => {
              const Icon = item.icon;
              return (
                <li key={item.id}>
                  <Link
                    to={item.link}
                    className="flex items-center gap-2 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <Icon className="text-lg" />
                    <span className="font-medium">{item.name}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>

      {/* Professional Side Navigation Menu */}
      <div
        className={`fixed top-0 right-0 w-[85%] max-w-sm h-screen z-50 transition-all duration-300 ease-out overflow-y-auto sidebar-scroll ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        {/* Professional background */}
        <div className="absolute inset-0 bg-white"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-blue-50 to-white"></div>

        {/* Professional border */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-600"></div>

        <div className="relative flex flex-col h-full">
          {/* Professional Header with close button */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-white">
            <h3 className="text-xl font-bold text-gray-800">PathLab Menu</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-300"
            >
              <FaXmark className="text-xl text-gray-600 hover:text-gray-800" />
            </button>
          </div>

          {/* Professional Navigation Items */}
          <div className="flex-1 p-6">
            <ul className="space-y-3 mb-8">
              {SideNavItems.map((item) => {
                const Icon = item.icon;
                return (
                  <li key={item.id}>
                    <Link
                      to={item.link}
                      className="flex items-center gap-3 p-4 rounded-lg hover:bg-blue-50 transition-all duration-200 group border border-transparent hover:border-blue-200"
                      onClick={() => setIsOpen(false)}
                    >
                      <Icon className="text-xl text-blue-600 group-hover:scale-110 transition-all duration-200" />
                      <span className="font-medium text-gray-700 group-hover:text-blue-700 transition-colors duration-200">
                        {item.name}
                      </span>
                    </Link>
                  </li>
                );
              })}
            </ul>

            {/* Professional Contact Information */}
            <div className="border-t border-gray-200 pt-6 mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                Contact Info
              </h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                  <FaMapLocationDot className="text-blue-600 text-lg" />
                  <span className="text-gray-700 text-sm">
                    Kathmandu, Nepal
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                  <FaPhone className="text-blue-600 text-lg" />
                  <span className="text-gray-700 text-sm">+977-1-4567890</span>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                  <FaEnvelope className="text-blue-600 text-lg" />
                  <span className="text-gray-700 text-sm">
                    <EMAIL>
                  </span>
                </div>
              </div>
            </div>

            {/* Professional Social Media */}
            <div className="border-t border-gray-200 pt-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                Follow Us
              </h4>
              <div className="flex gap-3 justify-center">
                <a
                  href="https://facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 hover:scale-110 transition-all duration-300 shadow-md"
                >
                  <FaFacebookF className="text-sm" />
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-sky-500 text-white rounded-full flex items-center justify-center hover:bg-sky-600 hover:scale-110 transition-all duration-300 shadow-md"
                >
                  <FaTwitter className="text-sm" />
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center hover:bg-pink-600 hover:scale-110 transition-all duration-300 shadow-md"
                >
                  <FaInstagram className="text-sm" />
                </a>
                <a
                  href="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-blue-700 text-white rounded-full flex items-center justify-center hover:bg-blue-800 hover:scale-110 transition-all duration-300 shadow-md"
                >
                  <FaLinkedinIn className="text-sm" />
                </a>
              </div>
            </div>
          </div>

          {/* Professional Footer Contact Button */}
          <div className="p-6 border-t border-gray-200">
            <Link
              to="/contact"
              className="flex items-center justify-center gap-3 p-4 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-300 w-full font-semibold shadow-md"
              onClick={() => setIsOpen(false)}
            >
              <FaPhone className="text-lg" />
              <span className="font-medium">Contact Us</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Bottom NavBar with Smooth Animations */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="md:hidden fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-700 z-40 shadow-2xl"
      >
        {/* Background glow effect */}
        <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent"></div>

        <div className="relative flex items-center justify-between px-2 py-1">
          {NavItems.map((item, index) => {
            const Icon = item.icon;
            const currentPath = location.pathname;
            const isCurrentPage = currentPath === item.link;
            const isActive = activeNavItem === item.id || isCurrentPage;

            return (
              <motion.div
                key={item.id}
                className="flex-1 relative"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.1,
                  ease: "easeOut",
                }}
              >
                <Link
                  to={item.link}
                  onClick={() => setActiveNavItem(item.id)}
                  className="flex flex-col items-center py-3 px-1 text-white relative bottom-nav-item"
                >
                  <AnimatePresence mode="wait">
                    {isActive ? (
                      <motion.div
                        key="active"
                        initial={{ scale: 0, y: 20, rotate: -180 }}
                        animate={{ scale: 1, y: 0, rotate: 0 }}
                        exit={{ scale: 0, y: 20, rotate: 180 }}
                        transition={{
                          type: "spring",
                          stiffness: 300,
                          damping: 20,
                          duration: 0.6,
                        }}
                        className="absolute -top-7 left-1/2 transform -translate-x-1/2"
                      >
                        <motion.div
                          className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-800 rounded-full flex items-center justify-center border-4 border-white shadow-2xl"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <motion.div
                            className="bg-gradient-to-br from-blue-500 to-blue-700 rounded-full w-12 h-12 flex items-center justify-center shadow-inner"
                            animate={{
                              boxShadow: [
                                "0 0 0 0 rgba(59, 130, 246, 0.7)",
                                "0 0 0 10px rgba(59, 130, 246, 0)",
                                "0 0 0 0 rgba(59, 130, 246, 0)",
                              ],
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                          >
                            <motion.div
                              animate={{
                                rotate: [0, 5, -5, 0],
                                scale: [1, 1.1, 1],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                              }}
                            >
                              <Icon className="text-2xl text-white" />
                            </motion.div>
                          </motion.div>
                        </motion.div>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="inactive"
                        initial={{ scale: 0, y: -10 }}
                        animate={{ scale: 1, y: 0 }}
                        exit={{ scale: 0, y: -10 }}
                        transition={{
                          type: "spring",
                          stiffness: 400,
                          damping: 25,
                          duration: 0.4,
                        }}
                        whileHover={{
                          scale: 1.2,
                          y: -3,
                          transition: { duration: 0.2 },
                        }}
                        whileTap={{ scale: 0.9 }}
                        className="mb-1"
                      >
                        <Icon className="text-xl text-white/80 hover:text-white transition-colors duration-200" />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <motion.span
                    animate={{
                      color: isActive ? "#fde047" : "#ffffff",
                      fontWeight: isActive ? "600" : "500",
                      y: isActive ? 24 : 0,
                      scale: isActive ? 0.9 : 0.8,
                    }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                    }}
                    className="text-[10px] whitespace-nowrap text-center leading-tight"
                  >
                    {item.name}
                  </motion.span>

                  {/* Active indicator line */}
                  <AnimatePresence>
                    {isActive && (
                      <motion.div
                        initial={{ scaleX: 0, opacity: 0 }}
                        animate={{ scaleX: 1, opacity: 1 }}
                        exit={{ scaleX: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-yellow-400 rounded-full"
                      />
                    )}
                  </AnimatePresence>
                </Link>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom glow effect */}
        <motion.div
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent"
          animate={{
            opacity: [0.5, 1, 0.5],
            scaleX: [0.8, 1.2, 0.8],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </motion.div>

      {/* Overlay for side menu */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};

export default PhoneNavBar;
