import React, { useState, useEffect } from "react";
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from "framer-motion";
import { Link, useLocation } from "react-router-dom";
import {
  FaHome,
  FaFileAlt,
  FaDownload,
  FaHeadset,
  FaShoppingCart,
  FaUser,
  FaSearch,
  FaPhone,
  FaEnvelope,
  FaBars,
  FaTimes,
  FaMapMarkerAlt,
  FaClock,
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaLinkedinIn,
  FaBell,
  FaHeart,
} from "react-icons/fa";

const DesktopNavBar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [hoveredItem, setHoveredItem] = useState(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const location = useLocation();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { id: 1, icon: FaHome, name: "Home", link: "/" },
    { id: 2, icon: FaFileAlt, name: "Book Service", link: "/service" },
    {
      id: 3,
      icon: FaDownload,
      name: "Download Report",
      link: "/download-report",
    },
  ];

  const quickActions = [
    {
      icon: FaBell,
      label: "Notifications",
      count: 3,
      action: () => setShowNotifications(!showNotifications),
    },

    { icon: FaShoppingCart, label: "Cart", count: 0 },
    { icon: FaUser, label: "Account" },
  ];

  const socialLinks = [
    {
      icon: FaFacebookF,
      href: "https://facebook.com",
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      icon: FaTwitter,
      href: "https://twitter.com",
      color: "bg-sky-500 hover:bg-sky-600",
    },
    {
      icon: FaInstagram,
      href: "https://instagram.com",
      color: "bg-pink-500 hover:bg-pink-600",
    },
    {
      icon: FaLinkedinIn,
      href: "https://linkedin.com",
      color: "bg-blue-700 hover:bg-blue-800",
    },
  ];

  return (
    <>
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled
            ? "bg-white/98 backdrop-blur-xl shadow-2xl border-b border-blue-100"
            : "bg-white/95 backdrop-blur-lg shadow-xl"
        }`}
      >
        {/* Professional Top Bar with Enhanced Contact Info */}
        <div className="hidden lg:block bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 text-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent"></div>
          <div className="absolute top-0 right-0 w-64 h-full bg-gradient-to-l from-white/5 to-transparent"></div>

          <div className="relative max-w-7xl mx-auto px-6 py-3">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-8">
                <motion.div
                  whileHover={{ scale: 1.05, y: -1 }}
                  className="flex items-center space-x-2 cursor-pointer group"
                >
                  <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300">
                    <FaPhone className="text-xs" />
                  </div>
                  <span className="font-medium">+977-1-4567890</span>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05, y: -1 }}
                  className="flex items-center space-x-2 cursor-pointer group"
                >
                  <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300">
                    <FaEnvelope className="text-xs" />
                  </div>
                  <span className="font-medium"><EMAIL></span>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05, y: -1 }}
                  className="flex items-center space-x-2 cursor-pointer group"
                >
                  <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300">
                    <FaMapMarkerAlt className="text-xs" />
                  </div>
                  <span className="font-medium">Kathmandu, Nepal</span>
                </motion.div>
              </div>
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <FaClock className="text-xs opacity-90" />
                  <span className="text-xs font-medium">
                    24/7 Emergency Services
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon;
                    return (
                      <motion.a
                        key={index}
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.2, y: -2 }}
                        whileTap={{ scale: 0.9 }}
                        className="w-7 h-7 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-300 group"
                      >
                        <Icon className="text-xs group-hover:scale-110 transition-transform duration-300" />
                      </motion.a>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Main Navigation */}
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-between h-20">
            {/* Professional Logo */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-4 cursor-pointer group"
            >
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-2xl flex items-center justify-center shadow-xl group-hover:shadow-2xl transition-all duration-300">
                  <span className="text-white font-bold text-2xl fredoka-font group-hover:scale-110 transition-transform duration-300">
                    P
                  </span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 fredoka-font group-hover:text-blue-700 transition-colors duration-300">
                  PathLab
                </h1>
                <p className="text-xs text-blue-600 font-medium -mt-1">
                  Medical Excellence
                </p>
              </div>
            </motion.div>

            {/* Professional Search Bar */}
            <div className="hidden md:flex flex-1 max-w-lg mx-8">
              <motion.div
                animate={{
                  scale: isSearchFocused ? 1.02 : 1,
                  boxShadow: isSearchFocused
                    ? "0 20px 40px rgba(59, 130, 246, 0.15)"
                    : "0 8px 25px rgba(0, 0, 0, 0.08)",
                }}
                className="relative w-full"
              >
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                  <FaSearch
                    className={`text-sm transition-colors duration-300 ${
                      isSearchFocused ? "text-blue-500" : "text-gray-400"
                    }`}
                  />
                </div>
                <input
                  type="text"
                  placeholder="Search tests, packages, health checkups..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  className="w-full pl-12 pr-12 py-4 bg-gradient-to-r from-gray-50 to-blue-50/30 border-2 border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-300 transition-all duration-300 text-sm placeholder-gray-500 font-medium"
                />
                {searchQuery && (
                  <motion.button
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    onClick={() => setSearchQuery("")}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-300"
                  >
                    <FaTimes className="text-sm" />
                  </motion.button>
                )}
                {isSearchFocused && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-gray-200 p-4 z-50"
                  >
                    <div className="text-sm text-gray-600 mb-2 font-medium">
                      Popular Searches:
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {[
                        "Blood Test",
                        "X-Ray",
                        "MRI Scan",
                        "Health Checkup",
                      ].map((item, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium cursor-pointer hover:bg-blue-100 transition-colors duration-200"
                        >
                          {item}
                        </span>
                      ))}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            </div>

            {/* Professional Navigation Items */}
            <div className="hidden lg:flex items-center space-x-2">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.link;

                return (
                  <motion.div
                    key={item.id}
                    onHoverStart={() => setHoveredItem(item.id)}
                    onHoverEnd={() => setHoveredItem(null)}
                    whileHover={{ y: -3 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      to={item.link}
                      className={`relative flex items-center space-x-3 px-5 py-3 rounded-2xl transition-all duration-300 group font-medium ${
                        isActive
                          ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl"
                          : "text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 hover:shadow-lg"
                      }`}
                    >
                      <Icon
                        className={`text-lg transition-all duration-300 ${
                          hoveredItem === item.id ? "scale-125 rotate-12" : ""
                        } ${isActive ? "text-white" : ""}`}
                      />
                      <span className="text-sm font-semibold">{item.name}</span>

                      {/* Enhanced Active indicator */}
                      {isActive && (
                        <>
                          <motion.div
                            layoutId="activeTab"
                            className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl -z-10"
                            initial={false}
                            transition={{
                              type: "spring",
                              stiffness: 500,
                              damping: 30,
                            }}
                          />
                          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full"></div>
                        </>
                      )}

                      {/* Hover effect */}
                      {hoveredItem === item.id && !isActive && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl -z-10"
                        />
                      )}
                    </Link>
                  </motion.div>
                );
              })}
            </div>

            {/* Enhanced Quick Actions */}
            <div className="flex items-center space-x-3">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <motion.button
                    key={index}
                    whileHover={{ scale: 1.15, y: -3 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={action.action}
                    className="relative p-3 text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 rounded-2xl transition-all duration-300 group shadow-md hover:shadow-xl"
                  >
                    <Icon className="text-xl group-hover:scale-110 transition-transform duration-300" />
                    {action.count !== undefined && action.count > 0 && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg"
                      >
                        {action.count}
                      </motion.span>
                    )}

                    {/* Enhanced Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gradient-to-r from-gray-800 to-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap shadow-xl border border-gray-700">
                      <div className="relative">
                        {action.label}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                      </div>
                    </div>
                  </motion.button>
                );
              })}

              {/* Professional Mobile Menu Button */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowDropdown(!showDropdown)}
                className="lg:hidden p-3 text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 rounded-2xl transition-all duration-300 shadow-md hover:shadow-xl"
              >
                <motion.div
                  animate={{ rotate: showDropdown ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {showDropdown ? (
                    <FaTimes className="text-xl" />
                  ) : (
                    <FaBars className="text-xl" />
                  )}
                </motion.div>
              </motion.button>
            </div>
          </div>
        </div>

        {/* Professional Mobile Dropdown Menu */}
        <AnimatePresence>
          {showDropdown && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="lg:hidden bg-gradient-to-b from-white to-blue-50/30 border-t border-blue-200 shadow-2xl"
            >
              <div className="max-w-7xl mx-auto px-6 py-6">
                {/* Enhanced Mobile Search */}
                <div className="mb-6">
                  <div className="relative">
                    <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-500 text-sm" />
                    <input
                      type="text"
                      placeholder="Search tests, packages..."
                      className="w-full pl-12 pr-4 py-4 bg-white border-2 border-blue-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-300 text-sm font-medium shadow-lg"
                    />
                  </div>
                </div>

                {/* Professional Mobile Navigation Items */}
                <div className="space-y-3">
                  {navItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = location.pathname === item.link;

                    return (
                      <motion.div
                        key={item.id}
                        whileHover={{ x: 8 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Link
                          to={item.link}
                          onClick={() => setShowDropdown(false)}
                          className={`flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group border ${
                            isActive
                              ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white border-blue-600 shadow-xl"
                              : "text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-blue-700 border-transparent hover:border-blue-200 hover:shadow-lg"
                          }`}
                        >
                          <Icon
                            className={`text-xl group-hover:scale-110 transition-transform duration-300 ${
                              isActive ? "text-white" : "text-blue-600"
                            }`}
                          />
                          <span className="font-semibold">{item.name}</span>
                        </Link>
                      </motion.div>
                    );
                  })}
                </div>

                {/* Mobile Contact Section */}
                <div className="mt-6 pt-6 border-t border-blue-200">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-xl border border-blue-200">
                      <FaPhone className="text-blue-600 text-sm" />
                      <span className="text-xs font-medium text-gray-700">
                        Emergency
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-xl border border-blue-200">
                      <FaEnvelope className="text-blue-600 text-sm" />
                      <span className="text-xs font-medium text-gray-700">
                        Contact
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Notifications Dropdown */}
        <AnimatePresence>
          {showNotifications && (
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="fixed top-24 right-8 w-80 bg-white rounded-2xl shadow-2xl border border-gray-200 z-50"
            >
              <div className="p-4 border-b border-gray-200">
                <h3 className="font-bold text-gray-800">Notifications</h3>
              </div>
              <div className="p-4 space-y-3 max-h-64 overflow-y-auto">
                {[1, 2, 3].map((_, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-3 p-3 hover:bg-blue-50 rounded-xl transition-colors duration-200"
                  >
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        Test Result Ready
                      </p>
                      <p className="text-xs text-gray-600">
                        Your blood test results are now available
                      </p>
                      <p className="text-xs text-blue-600 mt-1">2 hours ago</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Enhanced Floating Action Button for Emergency */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ delay: 1.2, duration: 0.6, type: "spring" }}
        className="fixed bottom-8 right-8 z-50"
      >
        <motion.button
          whileHover={{ scale: 1.15, rotate: 8 }}
          whileTap={{ scale: 0.9 }}
          className="w-16 h-16 bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white rounded-full shadow-2xl flex items-center justify-center group relative"
        >
          {/* Background hover effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>

          {/* Phone icon */}
          <FaPhone className="text-xl group-hover:animate-pulse relative z-10" />

          {/* Pulse animation */}
          <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-20"></div>

          {/* Enhanced Emergency tooltip - Fixed positioning */}
          <div className="absolute bottom-full left-1/12 transform -translate-x-1/2 mb-3 px-4 py-3 bg-gradient-to-r from-gray-800 to-gray-900 text-white text-sm rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap shadow-2xl border border-gray-700 z-50">
            <div className="text-center">
              <div className="font-bold text-white">Emergency Hotline</div>
              <div className="text-xs text-gray-200 mt-1">+977-1-4567890</div>
            </div>
            {/* Tooltip arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
          </div>
        </motion.button>
      </motion.div>
    </>
  );
};

export default DesktopNavBar;
