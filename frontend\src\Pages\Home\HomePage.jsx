import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Hero from "./Section/Hero";
import Banner from "./Section/Banner";
import MiniCard from "../../Components/ui/MiniCard";
import { FaCheckCircle, FaAward, FaClock, FaUsers } from "react-icons/fa";

const HomePage = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const benefits = [
    {
      icon: FaCheckCircle,
      title: "Accurate Results",
      description:
        "State-of-the-art equipment ensures precise and reliable test results every time.",
    },
    {
      icon: FaAward,
      title: "Certified Excellence",
      description:
        "Accredited laboratory with international quality standards and certifications.",
    },
    {
      icon: FaClock,
      title: "Quick Turnaround",
      description:
        "Fast processing times with most results available within 24-48 hours.",
    },
    {
      icon: FaUsers,
      title: "Expert Team",
      description:
        "Experienced medical professionals and technicians dedicated to your health.",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Sections */}
      <Banner />
      <Hero />

      {/* Services Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <div
            className={`text-center mb-12 transition-all duration-1000 ${isVisible ? "animate-fadeInUp" : "opacity-0"}`}
          >
            <h2 className="fredoka-font text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Our Services
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive medical testing solutions for all your healthcare
              needs
            </p>
          </div>
          <MiniCard />
        </div>
      </div>

      {/* Why Choose Us Section */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div
            className={`text-center mb-16 transition-all duration-1000 ${isVisible ? "animate-fadeInUp" : "opacity-0"}`}
          >
            <h2 className="fredoka-font text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Why Choose PathLab?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We combine cutting-edge technology with compassionate care to
              deliver exceptional medical testing services
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div
                  key={index}
                  className={`card-modern p-8 transition-all duration-700 delay-${index * 100} ${
                    isVisible ? "animate-fadeInScale" : "opacity-0"
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-black rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="text-xl text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto text-center px-6">
          <div
            className={`transition-all duration-1000 ${isVisible ? "animate-fadeInUp" : "opacity-0"}`}
          >
            <h2 className="fredoka-font text-4xl md:text-5xl font-bold mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Book your test today and experience the difference of professional
              medical testing
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary bg-white text-black hover:bg-gray-100 px-8 py-4 text-lg">
                Book Test Now
              </button>
              <button className="btn-secondary bg-transparent border-white text-white hover:bg-white hover:text-black px-8 py-4 text-lg">
                Contact Us
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
